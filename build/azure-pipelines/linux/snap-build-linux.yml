steps:
  - task: NodeTool@0
    inputs:
      versionSource: fromFile
      versionFilePath: .nvmrc
      nodejsMirror: https://github.com/joaomoreno/node-mirror/releases/download

  - task: DownloadPipelineArtifact@2
    displayName: "Download Pipeline Artifact"
    inputs:
      artifact: snap-$(VSCODE_ARCH)
      path: .build/linux/snap-tarball

  - script: |
      set -e

      # Get snapcraft version
      snapcraft --version

      # Make sure we get latest packages
      sudo apt-get update
      sudo apt-get upgrade -y
      sudo apt-get install -y curl apt-transport-https ca-certificates

      # Define variables
      SNAP_ROOT="$(pwd)/.build/linux/snap/$(VSCODE_ARCH)"

      # Unpack snap tarball artifact, in order to preserve file perms
      (cd .build/linux && tar -xzf snap-tarball/snap-$(VSCODE_ARCH).tar.gz)

      # Create snap package
      BUILD_VERSION="$(date +%s)"
      SNAP_FILENAME="code-$VSCODE_QUALITY-$(VSCODE_ARCH)-$BUILD_VERSION.snap"
      SNAP_PATH="$SNAP_ROOT/$SNAP_FILENAME"
      case $(VSCODE_ARCH) in
        x64) SNAPCRAFT_TARGET_ARGS="" ;;
        *) SNAPCRAFT_TARGET_ARGS="--target-arch $(VSCODE_ARCH)" ;;
      esac
      (cd $SNAP_ROOT/code-* && sudo --preserve-env snapcraft snap $SNAPCRAFT_TARGET_ARGS --output "$SNAP_PATH")
    displayName: Prepare for publish

  - script: |
      set -e
      SNAP_ROOT="$(pwd)/.build/linux/snap/$(VSCODE_ARCH)"
      SNAP_EXTRACTED_PATH=$(find $SNAP_ROOT -maxdepth 1 -type d -name 'code-*')
      SNAP_PATH=$(find $SNAP_ROOT -maxdepth 1 -type f -name '*.snap')

      # SBOM tool doesn't like recursive symlinks
      sudo find $SNAP_EXTRACTED_PATH -type l -delete

      echo "##vso[task.setvariable variable=SNAP_EXTRACTED_PATH]$SNAP_EXTRACTED_PATH"
      echo "##vso[task.setvariable variable=SNAP_PATH]$SNAP_PATH"
    target:
      container: host
    displayName: Find host snap path & prepare for SBOM

  - task: 1ES.PublishPipelineArtifact@1
    inputs:
      targetPath: $(SNAP_PATH)
      artifactName: vscode_client_linux_$(VSCODE_ARCH)_snap
      sbomBuildDropPath: $(SNAP_EXTRACTED_PATH)
      sbomPackageName: "VS Code Linux $(VSCODE_ARCH) SNAP"
      sbomPackageVersion: $(Build.SourceVersion)
    displayName: Publish snap package

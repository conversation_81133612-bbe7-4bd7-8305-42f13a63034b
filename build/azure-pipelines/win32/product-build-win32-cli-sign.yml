parameters:
  - name: VSCODE_BUILD_WIN32
    type: boolean
  - name: VSCODE_BUILD_WIN32_ARM64
    type: boolean
  - name: VSCODE_QUALITY
    type: string

steps:
  - task: NodeTool@0
    displayName: "Use Node.js"
    inputs:
      versionSource: fromFile
      versionFilePath: .nvmrc
      nodejsMirror: https://github.com/joaomoreno/node-mirror/releases/download

  - ${{ if ne(parameters.VSCODE_QUALITY, 'oss') }}:
    - task: AzureKeyVault@2
      displayName: "Azure Key Vault: Get Secrets"
      inputs:
        azureSubscription: vscode
        KeyVaultName: vscode-build-secrets
        SecretsFilter: "github-distro-mixin-password"

  - powershell: node build/setup-npm-registry.js $env:NPM_REGISTRY build
    condition: and(succeeded(), ne(variables['NPM_REGISTRY'], 'none'))
    displayName: Setup NPM Registry

  - powershell: |
      . build/azure-pipelines/win32/exec.ps1
      $ErrorActionPreference = "Stop"
      # Set the private NPM registry to the global npmrc file
      # so that authentication works for subfolders like build/, remote/, extensions/ etc
      # which does not have their own .npmrc file
      exec { npm config set registry "$env:NPM_REGISTRY" }
      $NpmrcPath = (npm config get userconfig)
      echo "##vso[task.setvariable variable=NPMRC_PATH]$NpmrcPath"
    condition: and(succeeded(), ne(variables['NPM_REGISTRY'], 'none'))
    displayName: Setup NPM

  - task: npmAuthenticate@0
    inputs:
      workingFile: $(NPMRC_PATH)
    condition: and(succeeded(), ne(variables['NPM_REGISTRY'], 'none'))
    displayName: Setup NPM Authentication

  - powershell: |
      . azure-pipelines/win32/exec.ps1
      $ErrorActionPreference = "Stop"
      exec { npm ci }
    workingDirectory: build
    env:
      GITHUB_TOKEN: "$(github-distro-mixin-password)"
    retryCountOnTaskFailure: 5
    displayName: Install build dependencies

  - template: ../cli/cli-win32-sign.yml@self
    parameters:
      VSCODE_CLI_ARTIFACTS:
        - ${{ if eq(parameters.VSCODE_BUILD_WIN32, true) }}:
          - unsigned_vscode_cli_win32_x64_cli
        - ${{ if eq(parameters.VSCODE_BUILD_WIN32_ARM64, true) }}:
          - unsigned_vscode_cli_win32_arm64_cli

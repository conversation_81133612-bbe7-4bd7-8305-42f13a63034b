parameters:
  - name: VSCODE_QUALITY
    type: string
  - name: VSCODE_BUILD_MACOS
    type: boolean
    default: false
  - name: VSCODE_BUILD_MACOS_ARM64
    type: boolean
    default: false
  - name: VSCODE_CHECK_ONLY
    type: boolean
    default: false

steps:
  - task: NodeTool@0
    inputs:
      versionSource: fromFile
      versionFilePath: .nvmrc
      nodejsMirror: https://github.com/joaomoreno/node-mirror/releases/download

  - ${{ if ne(parameters.VSCODE_QUALITY, 'oss') }}:
    - template: ../cli/cli-apply-patches.yml@self

  - task: Npm@1
    displayName: Download openssl prebuilt
    inputs:
      command: custom
      customCommand: pack @vscode-internal/openssl-prebuilt@0.0.11
      customRegistry: useFeed
      customFeed: "Monaco/openssl-prebuilt"
      workingDir: $(Build.ArtifactStagingDirectory)

  - script: |
      set -e
      mkdir $(Build.ArtifactStagingDirectory)/openssl
      tar -xvzf $(Build.ArtifactStagingDirectory)/vscode-internal-openssl-prebuilt-0.0.11.tgz --strip-components=1 --directory=$(Build.ArtifactStagingDirectory)/openssl
    displayName: Extract openssl prebuilt

  - template: ../cli/install-rust-posix.yml@self
    parameters:
      targets:
        - ${{ if eq(parameters.VSCODE_BUILD_MACOS, true) }}:
          - x86_64-apple-darwin
        - ${{ if eq(parameters.VSCODE_BUILD_MACOS_ARM64, true) }}:
          - aarch64-apple-darwin

  - ${{ if eq(parameters.VSCODE_BUILD_MACOS, true) }}:
    - template: ../cli/cli-compile.yml@self
      parameters:
        VSCODE_QUALITY: ${{ parameters.VSCODE_QUALITY }}
        VSCODE_CLI_TARGET: x86_64-apple-darwin
        VSCODE_CLI_ARTIFACT: unsigned_vscode_cli_darwin_x64_cli
        VSCODE_CHECK_ONLY: ${{ parameters.VSCODE_CHECK_ONLY }}
        VSCODE_CLI_ENV:
          OPENSSL_LIB_DIR: $(Build.ArtifactStagingDirectory)/openssl/x64-osx/lib
          OPENSSL_INCLUDE_DIR: $(Build.ArtifactStagingDirectory)/openssl/x64-osx/include

  - ${{ if eq(parameters.VSCODE_BUILD_MACOS_ARM64, true) }}:
    - template: ../cli/cli-compile.yml@self
      parameters:
        VSCODE_QUALITY: ${{ parameters.VSCODE_QUALITY }}
        VSCODE_CLI_TARGET: aarch64-apple-darwin
        VSCODE_CLI_ARTIFACT: unsigned_vscode_cli_darwin_arm64_cli
        VSCODE_CHECK_ONLY: ${{ parameters.VSCODE_CHECK_ONLY }}
        VSCODE_CLI_ENV:
          OPENSSL_LIB_DIR: $(Build.ArtifactStagingDirectory)/openssl/arm64-osx/lib
          OPENSSL_INCLUDE_DIR: $(Build.ArtifactStagingDirectory)/openssl/arm64-osx/include

  - ${{ if not(parameters.VSCODE_CHECK_ONLY) }}:
    - ${{ if eq(parameters.VSCODE_BUILD_MACOS, true) }}:
      - task: 1ES.PublishPipelineArtifact@1
        inputs:
          targetPath: $(Build.ArtifactStagingDirectory)/unsigned_vscode_cli_darwin_x64_cli.zip
          artifactName: unsigned_vscode_cli_darwin_x64_cli
          sbomBuildDropPath: $(Build.ArtifactStagingDirectory)/cli
          sbomPackageName: "VS Code macOS x64 CLI (unsigned)"
          sbomPackageVersion: $(Build.SourceVersion)
        displayName: Publish unsigned_vscode_cli_darwin_x64_cli artifact

    - ${{ if eq(parameters.VSCODE_BUILD_MACOS_ARM64, true) }}:
      - task: 1ES.PublishPipelineArtifact@1
        inputs:
          targetPath: $(Build.ArtifactStagingDirectory)/unsigned_vscode_cli_darwin_arm64_cli.zip
          artifactName: unsigned_vscode_cli_darwin_arm64_cli
          sbomBuildDropPath: $(Build.ArtifactStagingDirectory)/cli
          sbomPackageName: "VS Code macOS arm64 CLI (unsigned)"
          sbomPackageVersion: $(Build.SourceVersion)
        displayName: Publish unsigned_vscode_cli_darwin_arm64_cli artifact

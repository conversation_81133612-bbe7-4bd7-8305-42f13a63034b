[{"kind": 1, "language": "markdown", "value": "#### <PERSON>ros"}, {"kind": 2, "language": "github-issues", "value": "$REPOS=repo:microsoft/lsprotocol repo:microsoft/monaco-editor repo:microsoft/vscode repo:microsoft/vscode-anycode repo:microsoft/vscode-autopep8 repo:microsoft/vscode-black-formatter repo:microsoft/vscode-copilot repo:microsoft/vscode-copilot-release repo:microsoft/vscode-dev repo:microsoft/vscode-dev-chrome-launcher repo:microsoft/vscode-emmet-helper repo:microsoft/vscode-extension-telemetry repo:microsoft/vscode-flake8 repo:microsoft/vscode-github-issue-notebooks repo:microsoft/vscode-hexeditor repo:microsoft/vscode-internalbacklog repo:microsoft/vscode-isort repo:microsoft/vscode-js-debug repo:microsoft/vscode-jupyter repo:microsoft/vscode-jupyter-internal repo:microsoft/vscode-l10n repo:microsoft/vscode-livepreview repo:microsoft/vscode-markdown-languageservice repo:microsoft/vscode-markdown-tm-grammar repo:microsoft/vscode-mypy repo:microsoft/vscode-pull-request-github repo:microsoft/vscode-pylint repo:microsoft/vscode-python repo:microsoft/vscode-python-debugger repo:microsoft/vscode-python-tools-extension-template repo:microsoft/vscode-references-view repo:microsoft/vscode-remote-release repo:microsoft/vscode-remote-repositories-github repo:microsoft/vscode-remote-tunnels repo:microsoft/vscode-remotehub repo:microsoft/vscode-settings-sync-server repo:microsoft/vscode-unpkg repo:microsoft/vscode-vsce\r\n\r\n$MILESTONE=milestone:\"March 2025\""}, {"kind": 1, "language": "markdown", "value": "# Preparation"}, {"kind": 1, "language": "markdown", "value": "## Open Pull Requests on the Milestone"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MILESTONE is:pr is:open"}, {"kind": 1, "language": "markdown", "value": "## Unverified Older Insiders-Released Issues"}, {"kind": 2, "language": "github-issues", "value": "$REPOS -$MILESTONE is:issue is:closed reason:completed label:bug label:insiders-released -label:verified -label:*duplicate -label:*as-designed -label:z-author-verified -label:on-testplan -label:error-telemetry"}, {"kind": 1, "language": "markdown", "value": "## Unverified Older Insiders-Released Feature Requests"}, {"kind": 2, "language": "github-issues", "value": "$REPOS -$MILESTONE is:issue is:closed reason:completed label:feature-request label:insiders-released -label:on-testplan -label:verified -label:*duplicate -label:error-telemetry"}, {"kind": 1, "language": "markdown", "value": "## Open Issues on the Milestone"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MILESTONE is:issue is:open -label:iteration-plan -label:endgame-plan -label:testplan-item"}, {"kind": 1, "language": "markdown", "value": "## Feature Requests Missing Labels"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MILESTONE is:issue is:closed reason:completed label:feature-request -label:verification-needed -label:on-testplan -label:verified -label:*duplicate"}, {"kind": 1, "language": "markdown", "value": "## Open Test Plan Items without milestone"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MILESTONE is:issue is:open label:testplan-item no:milestone"}, {"kind": 1, "language": "markdown", "value": "# Testing"}, {"kind": 1, "language": "markdown", "value": "## Test Plan Items"}, {"kind": 2, "language": "github-issues", "value": "$REPOS is:issue is:open label:testplan-item"}, {"kind": 1, "language": "markdown", "value": "## Verification Needed"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MILESTONE is:issue is:closed reason:completed label:verification-needed -label:verified -label:on-testplan"}, {"kind": 1, "language": "markdown", "value": "# Verification"}, {"kind": 1, "language": "markdown", "value": "## Verifiable Fixes"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MILESTONE is:issue is:closed reason:completed sort:updated-asc label:bug -label:verified -label:on-testplan -label:*duplicate -label:duplicate -label:invalid -label:*as-designed -label:error-telemetry -label:verification-steps-needed -label:z-author-verified -label:unreleased -label:*not-reproducible -label:*out-of-scope"}, {"kind": 1, "language": "markdown", "value": "## Verifiable Fixes Missing Steps"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MILESTONE is:issue is:closed reason:completed sort:updated-asc label:bug label:verification-steps-needed -label:verified -label:on-testplan -label:*duplicate -label:duplicate -label:invalid -label:*as-designed -label:error-telemetry -label:z-author-verified -label:unreleased -label:*not-reproducible"}, {"kind": 1, "language": "markdown", "value": "## Unreleased Fixes"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MILESTONE is:issue is:closed reason:completed sort:updated-asc label:bug -label:verified -label:on-testplan -label:*duplicate -label:duplicate -label:invalid -label:*as-designed -label:error-telemetry -label:verification-steps-needed -label:z-author-verified label:unreleased -label:*not-reproducible"}, {"kind": 1, "language": "markdown", "value": "## All Unverified Fixes"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MILESTONE is:issue is:closed reason:completed sort:updated-asc label:bug -label:verified -label:on-testplan -label:*duplicate -label:duplicate -label:invalid -label:*as-designed -label:error-telemetry -label:z-author-verified -label:*not-reproducible"}, {"kind": 1, "language": "markdown", "value": "# Candidates"}, {"kind": 2, "language": "github-issues", "value": "$REPOS $MILESTONE is:issue is:open label:candidate"}]